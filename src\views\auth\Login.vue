<template>
  <div class="login-container">
    <div class="login-form">
      <div class="form-header">
        <h1>石油企业管理系统</h1>
        <p>请登录您的账户</p>
        <div class="dev-hint" v-if="isDevelopment">
          <el-alert
            title="开发模式提示"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <p>测试账号：admin 或 test</p>
              <p>密码：任意6位以上字符</p>
            </template>
          </el-alert>
        </div>
      </div>

      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="rules"
        class="login-form-content"
        @submit.prevent="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="用户名"
            size="large"
            :prefix-icon="User"
          />
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="密码"
            size="large"
            :prefix-icon="Lock"
            show-password
            @keyup.enter="handleLogin"
          />
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            size="large"
            :loading="loading"
            @click="handleLogin"
            class="login-button"
          >
            登录
          </el-button>
        </el-form-item>

        <div class="form-footer">
          <router-link to="/register" class="register-link">
            还没有账户？立即注册
          </router-link>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script>
import { reactive, ref, computed } from "vue";
import { useStore } from "vuex";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { User, Lock } from "@element-plus/icons-vue";

export default {
  name: "Login",
  setup() {
    const store = useStore();
    const router = useRouter();
    const loginFormRef = ref();
    const loading = ref(false);

    const loginForm = reactive({
      username: "",
      password: "",
    });

    const isDevelopment = computed(
      () => process.env.NODE_ENV === "development"
    );

    const rules = {
      username: [{ required: true, message: "请输入用户名", trigger: "blur" }],
      password: [
        { required: true, message: "请输入密码", trigger: "blur" },
        { min: 6, message: "密码长度至少6位", trigger: "blur" },
      ],
    };

    const handleLogin = async () => {
      try {
        await loginFormRef.value.validate();
        loading.value = true;

        // 在开发环境下，如果是测试账号，直接设置登录状态
        if (
          process.env.NODE_ENV === "development" &&
          (loginForm.username === "admin" || loginForm.username === "test")
        ) {
          console.log("Dev mode - Setting test user login state");

          // 直接设置认证状态
          store.commit("auth/SET_TOKEN", "dev-test-token");
          store.commit("auth/SET_USER", {
            id: 1,
            username: loginForm.username,
            email: `${loginForm.username}@example.com`,
          });
          store.commit("auth/SET_ROLES", ["ROLE_ADMIN", "ROLE_MANAGER"]);

          // 保存token到localStorage
          import("@/utils/auth").then(({ setToken }) => {
            setToken("dev-test-token");
          });

          ElMessage.success("登录成功（开发模式）");
          router.push("/dashboard");
        } else {
          // 正常的API登录流程
          await store.dispatch("auth/login", loginForm);
          ElMessage.success("登录成功");
          router.push("/dashboard");
        }
      } catch (error) {
        console.error("Login error:", error);
        ElMessage.error(error.response?.data?.message || "登录失败");
      } finally {
        loading.value = false;
      }
    };

    return {
      loginForm,
      rules,
      loading,
      loginFormRef,
      handleLogin,
      isDevelopment,
      User,
      Lock,
    };
  },
};
</script>

<style lang="scss" scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: $spacing-lg;
}

.login-form {
  background: $bg-primary;
  border-radius: $border-radius-large;
  box-shadow: $shadow-heavy;
  padding: $spacing-xl;
  width: 100%;
  max-width: 400px;

  .form-header {
    text-align: center;
    margin-bottom: $spacing-xl;

    h1 {
      font-size: 24px;
      font-weight: 600;
      color: $text-primary;
      margin-bottom: $spacing-sm;
    }

    p {
      color: $text-secondary;
      font-size: 14px;
      margin-bottom: $spacing-md;
    }

    .dev-hint {
      margin-top: $spacing-md;
      text-align: left;

      :deep(.el-alert__content) {
        p {
          margin: 2px 0;
          font-size: 12px;
        }
      }
    }
  }

  .login-form-content {
    .el-form-item {
      margin-bottom: $spacing-lg;
    }

    .login-button {
      width: 100%;
      height: 48px;
      font-size: 16px;
      font-weight: 500;
    }
  }

  .form-footer {
    text-align: center;
    margin-top: $spacing-lg;

    .register-link {
      color: $primary-color;
      text-decoration: none;
      font-size: 14px;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}
</style>
