<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
import { onMounted } from "vue";
import { useStore } from "vuex";

export default {
  name: "App",
  setup() {
    const store = useStore();

    onMounted(async () => {
      // 在组件挂载后检查用户登录状态
      console.log("App - Initializing auth state");
      await store.dispatch("auth/checkAuth");
    });

    return {};
  },
};
</script>

<style lang="scss">
#app {
  height: 100vh;
  overflow: hidden;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: #f5f7fa;
}
</style>
