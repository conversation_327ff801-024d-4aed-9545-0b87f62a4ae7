<template>
  <div class="page-container">
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-title">活跃项目</div>
        <div class="stat-value">{{ stats.activeProjects }}</div>
        <div class="stat-change positive">
          <el-icon><TrendCharts /></el-icon>
          +12% 本月
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-title">团队数量</div>
        <div class="stat-value">{{ stats.totalTeams }}</div>
        <div class="stat-change positive">
          <el-icon><TrendCharts /></el-icon>
          +5% 本月
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-title">本月收入</div>
        <div class="stat-value">¥{{ formatNumber(stats.monthlyIncome) }}</div>
        <div class="stat-change positive">
          <el-icon><TrendCharts /></el-icon>
          +23% 环比
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-title">总用户数</div>
        <div class="stat-value">{{ stats.totalUsers }}</div>
        <div class="stat-change positive">
          <el-icon><TrendCharts /></el-icon>
          +8% 本月
        </div>
      </div>
    </div>

    <div class="dashboard-content">
      <div class="content-card">
        <div class="card-header">
          <h2>项目进度概览</h2>
        </div>
        <div class="card-body">
          <div id="projectChart" style="height: 300px"></div>
        </div>
      </div>

      <div class="content-card" style="margin-top: 24px">
        <div class="card-header">
          <h2>财务趋势</h2>
        </div>
        <div class="card-body">
          <div id="financialChart" style="height: 300px"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from "vue";
import * as echarts from "echarts";
import { TrendCharts } from "@element-plus/icons-vue";

export default {
  name: "Dashboard",
  components: {
    TrendCharts,
  },
  setup() {
    const stats = ref({
      activeProjects: 8,
      totalTeams: 12,
      monthlyIncome: 15600000,
      totalUsers: 156,
    });

    const formatNumber = (number) => {
      return (number / 10000).toFixed(0) + "万";
    };

    const initCharts = () => {
      // 项目进度图表
      const projectChart = echarts.init(
        document.getElementById("projectChart")
      );
      const projectOption = {
        title: {
          text: "项目状态分布",
          left: "center",
        },
        tooltip: {
          trigger: "item",
        },
        legend: {
          orient: "vertical",
          left: "left",
        },
        series: [
          {
            name: "项目状态",
            type: "pie",
            radius: "50%",
            data: [
              { value: 5, name: "进行中" },
              { value: 2, name: "计划中" },
              { value: 1, name: "已完成" },
              { value: 1, name: "暂停" },
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
        ],
      };
      projectChart.setOption(projectOption);

      // 财务趋势图表
      const financialChart = echarts.init(
        document.getElementById("financialChart")
      );
      const financialOption = {
        title: {
          text: "月度财务趋势",
          left: "center",
        },
        tooltip: {
          trigger: "axis",
        },
        legend: {
          data: ["收入", "支出"],
          top: 30,
        },
        xAxis: {
          type: "category",
          data: ["1月", "2月", "3月", "4月", "5月", "6月", "7月"],
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            name: "收入",
            type: "line",
            data: [1200, 1500, 1800, 1600, 2000, 1900, 2200],
          },
          {
            name: "支出",
            type: "line",
            data: [800, 1200, 1400, 1100, 1500, 1300, 1600],
          },
        ],
      };
      financialChart.setOption(financialOption);

      // 响应式处理
      window.addEventListener("resize", () => {
        projectChart.resize();
        financialChart.resize();
      });
    };

    onMounted(() => {
      initCharts();
    });

    return {
      stats,
      formatNumber,
    };
  },
};
</script>

<style lang="scss" scoped>
.dashboard-content {
  .content-card + .content-card {
    margin-top: $spacing-lg;
  }
}
</style>
