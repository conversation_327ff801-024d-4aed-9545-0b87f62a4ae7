<template>
  <div class="project-form-container">
    <div class="page-header">
      <h2>{{ isEdit ? "编辑项目" : "创建项目" }}</h2>
      <el-button @click="$router.go(-1)" :icon="ArrowLeft"> 返回 </el-button>
    </div>

    <el-card>
      <el-form
        :model="projectForm"
        :rules="formRules"
        ref="formRef"
        label-width="120px"
        @submit.prevent="handleSubmit"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目名称" prop="name">
              <el-input
                v-model="projectForm.name"
                placeholder="请输入项目名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目状态" prop="status">
              <el-select
                v-model="projectForm.status"
                placeholder="请选择项目状态"
                style="width: 100%"
              >
                <el-option label="进行中" value="IN_PROGRESS" />
                <el-option label="已完成" value="COMPLETED" />
                <el-option label="已暂停" value="PAUSED" />
                <el-option label="已取消" value="CANCELLED" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="项目描述" prop="description">
          <el-input
            v-model="projectForm.description"
            type="textarea"
            :rows="4"
            placeholder="请输入项目描述"
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="项目预算" prop="budget">
              <el-input
                v-model.number="projectForm.budget"
                placeholder="请输入项目预算"
                type="number"
                :min="0"
              >
                <template #prepend>¥</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="开始日期" prop="startDate">
              <el-date-picker
                v-model="projectForm.startDate"
                type="date"
                placeholder="选择开始日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="结束日期" prop="endDate">
              <el-date-picker
                v-model="projectForm.endDate"
                type="date"
                placeholder="选择结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目经理" prop="managerId">
              <el-select
                v-model="projectForm.managerId"
                placeholder="请选择项目经理"
                style="width: 100%"
                filterable
              >
                <el-option
                  v-for="user in userList"
                  :key="user.id"
                  :label="user.username"
                  :value="user.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目进度" prop="progress">
              <el-slider
                v-model="projectForm.progress"
                :min="0"
                :max="100"
                :step="5"
                show-input
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="团队成员" prop="teamMembers">
          <el-select
            v-model="projectForm.teamMembers"
            multiple
            placeholder="请选择团队成员"
            style="width: 100%"
            filterable
          >
            <el-option
              v-for="user in userList"
              :key="user.id"
              :label="user.username"
              :value="user.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="项目标签">
          <el-tag
            v-for="tag in projectForm.tags"
            :key="tag"
            closable
            @close="removeTag(tag)"
            style="margin-right: 10px"
          >
            {{ tag }}
          </el-tag>
          <el-input
            v-if="inputVisible"
            ref="inputRef"
            v-model="inputValue"
            size="small"
            style="width: 100px"
            @keyup.enter="handleInputConfirm"
            @blur="handleInputConfirm"
          />
          <el-button v-else size="small" @click="showInput" :icon="Plus">
            添加标签
          </el-button>
        </el-form-item>

        <el-form-item label="项目文档">
          <el-upload
            :file-list="fileList"
            :before-upload="beforeUpload"
            :on-remove="handleRemove"
            multiple
            drag
          >
            <el-icon style="font-size: 67px; color: #c0c4cc">
              <UploadFilled />
            </el-icon>
            <div style="color: #606266; font-size: 14px">
              将文件拖到此处，或<em>点击上传</em>
            </div>
          </el-upload>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            {{ isEdit ? "更新项目" : "创建项目" }}
          </el-button>
          <el-button @click="$router.go(-1)"> 取消 </el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, nextTick } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { ArrowLeft, Plus, UploadFilled } from "@element-plus/icons-vue";
// import { projectApi } from '@/api/project'
// import { userApi } from '@/api/user'

export default {
  name: "ProjectForm",
  setup() {
    const route = useRoute();
    const router = useRouter();
    const formRef = ref(null);
    const inputRef = ref(null);
    const submitting = ref(false);
    const inputVisible = ref(false);
    const inputValue = ref("");

    const isEdit = computed(() => !!route.params.id);

    const projectForm = reactive({
      name: "",
      description: "",
      status: "IN_PROGRESS",
      budget: null,
      startDate: "",
      endDate: "",
      managerId: null,
      progress: 0,
      teamMembers: [],
      tags: [],
    });

    const userList = ref([
      { id: 1, username: "张三" },
      { id: 2, username: "李四" },
      { id: 3, username: "王五" },
    ]);

    const fileList = ref([]);

    const formRules = {
      name: [
        { required: true, message: "请输入项目名称", trigger: "blur" },
        {
          min: 2,
          max: 50,
          message: "项目名称长度在 2 到 50 个字符",
          trigger: "blur",
        },
      ],
      description: [
        { required: true, message: "请输入项目描述", trigger: "blur" },
        {
          min: 10,
          max: 500,
          message: "项目描述长度在 10 到 500 个字符",
          trigger: "blur",
        },
      ],
      status: [
        { required: true, message: "请选择项目状态", trigger: "change" },
      ],
      budget: [
        { required: true, message: "请输入项目预算", trigger: "blur" },
        {
          type: "number",
          min: 0,
          message: "预算必须大于等于0",
          trigger: "blur",
        },
      ],
      startDate: [
        { required: true, message: "请选择开始日期", trigger: "change" },
      ],
      endDate: [
        { required: true, message: "请选择结束日期", trigger: "change" },
      ],
      managerId: [
        { required: true, message: "请选择项目经理", trigger: "change" },
      ],
    };

    // 获取项目详情
    const getProjectDetail = async (id) => {
      try {
        // const response = await projectApi.getProject(id)
        // Object.assign(projectForm, response.data)

        // 模拟数据
        Object.assign(projectForm, {
          name: "企业管理系统",
          description: "基于Vue3和SpringBoot的企业管理系统",
          status: "IN_PROGRESS",
          budget: 500000,
          startDate: "2024-01-01",
          endDate: "2024-06-30",
          managerId: 1,
          progress: 75,
          teamMembers: [1, 2],
          tags: ["Vue3", "SpringBoot", "企业级"],
        });
      } catch (error) {
        ElMessage.error("获取项目详情失败");
        router.go(-1);
      }
    };

    // 获取用户列表
    const getUserList = async () => {
      try {
        // const response = await userApi.getUserList({ size: 100 })
        // userList.value = response.data.content
      } catch (error) {
        console.error("获取用户列表失败");
      }
    };

    // 提交表单
    const handleSubmit = async () => {
      try {
        const valid = await formRef.value.validate();
        if (!valid) return;

        if (new Date(projectForm.endDate) <= new Date(projectForm.startDate)) {
          ElMessage.error("结束日期必须晚于开始日期");
          return;
        }

        submitting.value = true;

        if (isEdit.value) {
          // await projectApi.updateProject(route.params.id, projectForm)
          ElMessage.success("项目更新成功");
        } else {
          // await projectApi.createProject(projectForm)
          ElMessage.success("项目创建成功");
        }

        router.push("/projects");
      } catch (error) {
        ElMessage.error(error.message || "操作失败");
      } finally {
        submitting.value = false;
      }
    };

    // 标签相关
    const showInput = () => {
      inputVisible.value = true;
      nextTick(() => {
        inputRef.value?.focus();
      });
    };

    const handleInputConfirm = () => {
      if (inputValue.value && !projectForm.tags.includes(inputValue.value)) {
        projectForm.tags.push(inputValue.value);
      }
      inputVisible.value = false;
      inputValue.value = "";
    };

    const removeTag = (tag) => {
      const index = projectForm.tags.indexOf(tag);
      if (index > -1) {
        projectForm.tags.splice(index, 1);
      }
    };

    // 文件上传
    const beforeUpload = (file) => {
      const isValidType = [
        "application/pdf",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      ].includes(file.type);
      const isLt10M = file.size / 1024 / 1024 < 10;

      if (!isValidType) {
        ElMessage.error("只能上传 PDF、DOC、DOCX 格式的文件！");
        return false;
      }
      if (!isLt10M) {
        ElMessage.error("文件大小不能超过 10MB！");
        return false;
      }
      return true;
    };

    const handleRemove = (file) => {
      const index = fileList.value.indexOf(file);
      if (index > -1) {
        fileList.value.splice(index, 1);
      }
    };

    onMounted(() => {
      getUserList();
      if (isEdit.value) {
        getProjectDetail(route.params.id);
      }
    });

    return {
      isEdit,
      formRef,
      inputRef,
      submitting,
      inputVisible,
      inputValue,
      projectForm,
      userList,
      fileList,
      formRules,
      handleSubmit,
      showInput,
      handleInputConfirm,
      removeTag,
      beforeUpload,
      handleRemove,
      ArrowLeft,
      Plus,
      UploadFilled,
    };
  },
};
</script>

<style lang="scss" scoped>
.project-form-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    margin: 0;
    color: #303133;
  }
}

:deep(.el-card__body) {
  padding: 30px;
}

:deep(.el-upload-dragger) {
  width: 100%;
}
</style>
