import { createRouter, createWebHistory } from 'vue-router'
import store from '@/store'

const routes = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/Login.vue'),
    meta: { guest: true }
  },
  {
    path: '/register',
    name: 'Register',
    component: () => import('@/views/auth/Register.vue'),
    meta: { guest: true }
  },
  {
    path: '/',
    component: () => import('@/layout/Layout.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: { title: '仪表盘' }
      },
      {
        path: 'users',
        name: 'Users',
        component: () => import('@/views/user/UserList.vue'),
        meta: { title: '用户管理', roles: ['ROLE_ADMIN', 'ROLE_MANAGER'] }
      },
      {
        path: 'projects',
        name: 'Projects',
        component: () => import('@/views/project/ProjectList.vue'),
        meta: { title: '项目管理' }
      },
      {
        path: 'projects/create',
        name: 'ProjectCreate',
        component: () => import('@/views/project/ProjectForm.vue'),
        meta: { title: '创建项目', roles: ['ROLE_ADMIN', 'ROLE_MANAGER'] }
      },
      {
        path: 'projects/:id/edit',
        name: 'ProjectEdit',
        component: () => import('@/views/project/ProjectForm.vue'),
        meta: { title: '编辑项目', roles: ['ROLE_ADMIN', 'ROLE_MANAGER'] }
      },
      {
        path: 'teams',
        name: 'Teams',
        component: () => import('@/views/team/TeamList.vue'),
        meta: { title: '团队管理' }
      },
      {
        path: 'financial',
        name: 'Financial',
        component: () => import('@/views/financial/FinancialList.vue'),
        meta: { title: '财务管理', roles: ['ROLE_ADMIN', 'ROLE_MANAGER'] }
      },
      {
        path: 'reports',
        name: 'Reports',
        component: () => import('@/views/report/ReportDashboard.vue'),
        meta: { title: '报表展示' }
      },
      {
        path: 'profile',
        name: 'Profile',
        component: () => import('@/views/user/Profile.vue'),
        meta: { title: '个人资料' }
      },
      {
        path: 'api-test',
        name: 'ApiTest',
        component: () => import('@/views/ApiTest.vue'),
        meta: { title: 'API测试', roles: ['ROLE_ADMIN'] }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  // 防止无限重定向
  if (from.path === to.path) {
    console.log('Router Guard - Preventing infinite redirect')
    next()
    return
  }

  // 确保auth状态已初始化
  await store.dispatch('auth/checkAuth')

  const isAuthenticated = store.getters['auth/isAuthenticated']
  const userRoles = store.getters['auth/userRoles']

  console.log('Router Guard - Navigating from:', from.path, 'to:', to.path)
  console.log('Router Guard - isAuthenticated:', isAuthenticated)
  console.log('Router Guard - userRoles:', userRoles)
  console.log('Router Guard - Route meta:', to.meta)

  // 如果是根路径，重定向到dashboard或login
  if (to.path === '/') {
    if (isAuthenticated) {
      console.log('Router Guard - Root path, redirecting to dashboard')
      next('/dashboard')
    } else {
      console.log('Router Guard - Root path, redirecting to login')
      next('/login')
    }
    return
  }

  // 需要认证的路由
  if (to.meta.requiresAuth && !isAuthenticated) {
    console.log('Router Guard - Redirecting to login (not authenticated)')
    next('/login')
    return
  }

  // 访客路由（如登录页），已认证用户不应访问
  if (to.meta.guest && isAuthenticated) {
    console.log('Router Guard - Redirecting to dashboard (guest route but authenticated)')
    next('/dashboard')
    return
  }

  // 角色权限检查
  if (to.meta.roles && to.meta.roles.length > 0) {
    if (!userRoles || !to.meta.roles.some(role => userRoles.includes(role))) {
      console.log('Router Guard - Redirecting to dashboard (insufficient permissions)')
      console.log('Required roles:', to.meta.roles)
      console.log('User roles:', userRoles)
      next('/dashboard')
      return
    }
  }

  console.log('Router Guard - Allowing navigation')
  next()
})

export default router