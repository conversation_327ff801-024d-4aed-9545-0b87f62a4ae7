<template>
  <div class="project-list-container">
    <div class="page-header">
      <h2>项目管理</h2>
      <el-button
        type="primary"
        @click="$router.push('/projects/create')"
        :icon="Plus"
      >
        创建项目
      </el-button>
    </div>

    <div class="search-bar">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchForm.name"
            placeholder="搜索项目名称"
            :prefix-icon="Search"
            clearable
            @clear="handleSearch"
            @keyup.enter="handleSearch"
          />
        </el-col>
        <el-col :span="6">
          <el-select
            v-model="searchForm.status"
            placeholder="选择状态"
            clearable
            @change="handleSearch"
          >
            <el-option label="进行中" value="IN_PROGRESS" />
            <el-option label="已完成" value="COMPLETED" />
            <el-option label="已暂停" value="PAUSED" />
            <el-option label="已取消" value="CANCELLED" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleSearch"
          />
        </el-col>
        <el-col :span="6">
          <el-button type="primary" @click="handleSearch" :icon="Search">
            搜索
          </el-button>
          <el-button @click="resetSearch" :icon="Refresh"> 重置 </el-button>
        </el-col>
      </el-row>
    </div>

    <el-table
      :data="projectList"
      v-loading="loading"
      stripe
      border
      style="width: 100%"
    >
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="name" label="项目名称" />
      <el-table-column
        prop="description"
        label="项目描述"
        show-overflow-tooltip
      />
      <el-table-column prop="status" label="状态" width="120">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)" size="small">
            {{ getStatusLabel(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="budget" label="预算" width="120">
        <template #default="{ row }">
          ¥{{ row.budget?.toLocaleString() || "0" }}
        </template>
      </el-table-column>
      <el-table-column prop="startDate" label="开始日期" width="120">
        <template #default="{ row }">
          {{ formatDate(row.startDate) }}
        </template>
      </el-table-column>
      <el-table-column prop="endDate" label="结束日期" width="120">
        <template #default="{ row }">
          {{ formatDate(row.endDate) }}
        </template>
      </el-table-column>
      <el-table-column prop="progress" label="进度" width="120">
        <template #default="{ row }">
          <el-progress
            :percentage="row.progress || 0"
            :status="row.progress === 100 ? 'success' : ''"
            :stroke-width="6"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button
            type="primary"
            size="small"
            @click="handleView(row)"
            :icon="View"
          >
            查看
          </el-button>
          <el-button
            type="warning"
            size="small"
            @click="handleEdit(row)"
            :icon="Edit"
          >
            编辑
          </el-button>
          <el-button
            type="danger"
            size="small"
            @click="handleDelete(row)"
            :icon="Delete"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  Plus,
  Search,
  Refresh,
  View,
  Edit,
  Delete,
} from "@element-plus/icons-vue";
// import { projectApi } from '@/api/project'

export default {
  name: "ProjectList",
  setup() {
    const router = useRouter();
    const loading = ref(false);

    const projectList = ref([]);
    const currentPage = ref(1);
    const pageSize = ref(10);
    const total = ref(0);

    const searchForm = reactive({
      name: "",
      status: "",
      dateRange: [],
    });

    // 模拟数据
    const mockData = [
      {
        id: 1,
        name: "企业管理系统",
        description: "基于Vue3和SpringBoot的企业管理系统",
        status: "IN_PROGRESS",
        budget: 500000,
        startDate: "2024-01-01",
        endDate: "2024-06-30",
        progress: 75,
      },
      {
        id: 2,
        name: "移动端应用",
        description: "面向客户的移动端应用开发",
        status: "COMPLETED",
        budget: 300000,
        startDate: "2023-09-01",
        endDate: "2023-12-31",
        progress: 100,
      },
    ];

    // 获取项目列表
    const getProjectList = async () => {
      try {
        loading.value = true;
        // 模拟API调用
        setTimeout(() => {
          projectList.value = mockData;
          total.value = mockData.length;
          loading.value = false;
        }, 500);

        // 实际API调用
        // const params = {
        //   page: currentPage.value,
        //   size: pageSize.value,
        //   ...searchForm
        // }
        // const response = await projectApi.getProjectList(params)
        // projectList.value = response.data.content
        // total.value = response.data.totalElements
      } catch (error) {
        ElMessage.error("获取项目列表失败");
        loading.value = false;
      }
    };

    // 搜索
    const handleSearch = () => {
      currentPage.value = 1;
      getProjectList();
    };

    // 重置搜索
    const resetSearch = () => {
      Object.assign(searchForm, {
        name: "",
        status: "",
        dateRange: [],
      });
      handleSearch();
    };

    // 分页
    const handleSizeChange = (size) => {
      pageSize.value = size;
      getProjectList();
    };

    const handleCurrentChange = (page) => {
      currentPage.value = page;
      getProjectList();
    };

    // 查看项目
    const handleView = (row) => {
      ElMessage.info(`查看项目: ${row.name}`);
      // 跳转到项目详情页
    };

    // 编辑项目
    const handleEdit = (row) => {
      router.push(`/projects/${row.id}/edit`);
    };

    // 删除项目
    const handleDelete = async (row) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除项目 "${row.name}" 吗？此操作不可恢复！`,
          "确认删除",
          { type: "warning" }
        );

        // await projectApi.deleteProject(row.id)
        ElMessage.success("删除成功");
        getProjectList();
      } catch (error) {
        if (error !== "cancel") {
          ElMessage.error("删除失败");
        }
      }
    };

    // 工具函数
    const getStatusType = (status) => {
      const statusTypes = {
        IN_PROGRESS: "primary",
        COMPLETED: "success",
        PAUSED: "warning",
        CANCELLED: "danger",
      };
      return statusTypes[status] || "info";
    };

    const getStatusLabel = (status) => {
      const statusLabels = {
        IN_PROGRESS: "进行中",
        COMPLETED: "已完成",
        PAUSED: "已暂停",
        CANCELLED: "已取消",
      };
      return statusLabels[status] || status;
    };

    const formatDate = (dateString) => {
      if (!dateString) return "-";
      return new Date(dateString).toLocaleDateString();
    };

    onMounted(() => {
      getProjectList();
    });

    return {
      loading,
      projectList,
      currentPage,
      pageSize,
      total,
      searchForm,
      getProjectList,
      handleSearch,
      resetSearch,
      handleSizeChange,
      handleCurrentChange,
      handleView,
      handleEdit,
      handleDelete,
      getStatusType,
      getStatusLabel,
      formatDate,
      Plus,
      Search,
      Refresh,
      View,
      Edit,
      Delete,
    };
  },
};
</script>

<style lang="scss" scoped>
.project-list-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    margin: 0;
    color: #303133;
  }
}

.search-bar {
  margin-bottom: 20px;
  padding: 20px;
  background: #f5f7fa;
  border-radius: 4px;
}

.pagination {
  margin-top: 20px;
  text-align: center;
}
</style>
