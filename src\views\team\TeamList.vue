<template>
  <div class="team-list-container">
    <div class="page-header">
      <h2>团队管理</h2>
      <el-button type="primary" @click="showAddDialog" :icon="Plus">
        创建团队
      </el-button>
    </div>

    <div class="search-bar">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-input
            v-model="searchForm.name"
            placeholder="搜索团队名称"
            :prefix-icon="Search"
            clearable
            @clear="handleSearch"
            @keyup.enter="handleSearch"
          />
        </el-col>
        <el-col :span="8">
          <el-button type="primary" @click="handleSearch" :icon="Search">
            搜索
          </el-button>
          <el-button @click="resetSearch" :icon="Refresh"> 重置 </el-button>
        </el-col>
      </el-row>
    </div>

    <el-row :gutter="20">
      <el-col
        v-for="team in teamList"
        :key="team.id"
        :span="8"
        style="margin-bottom: 20px"
      >
        <el-card class="team-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span class="team-name">{{ team.name }}</span>
              <el-dropdown>
                <el-button type="text" :icon="MoreFilled" />
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="handleEdit(team)">
                      <el-icon><Edit /></el-icon>
                      编辑
                    </el-dropdown-item>
                    <el-dropdown-item @click="handleDelete(team)" divided>
                      <el-icon><Delete /></el-icon>
                      删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>

          <div class="team-content">
            <div class="team-info">
              <p class="description">{{ team.description || "暂无描述" }}</p>
              <div class="stats">
                <el-tag size="small">
                  成员: {{ team.memberCount || 0 }}人
                </el-tag>
                <el-tag type="success" size="small">
                  项目: {{ team.projectCount || 0 }}个
                </el-tag>
              </div>
            </div>

            <div class="team-members">
              <div class="member-avatars">
                <el-avatar
                  v-for="(member, index) in team.members?.slice(0, 5)"
                  :key="member.id"
                  :src="member.avatar"
                  size="small"
                  :style="{ marginLeft: index > 0 ? '-8px' : '0' }"
                >
                  {{ member.username?.charAt(0) }}
                </el-avatar>
                <span v-if="team.members?.length > 5" class="more-members">
                  +{{ team.members.length - 5 }}
                </span>
              </div>
              <el-button type="text" size="small" @click="viewMembers(team)">
                查看成员
              </el-button>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <div class="pagination" v-if="total > 0">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[9, 18, 36]"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 团队表单对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="500px"
      @close="resetForm"
    >
      <el-form
        :model="teamForm"
        :rules="formRules"
        ref="formRef"
        label-width="100px"
      >
        <el-form-item label="团队名称" prop="name">
          <el-input v-model="teamForm.name" placeholder="请输入团队名称" />
        </el-form-item>
        <el-form-item label="团队描述" prop="description">
          <el-input
            v-model="teamForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入团队描述"
          />
        </el-form-item>
        <el-form-item label="团队成员" prop="members">
          <el-select
            v-model="teamForm.members"
            multiple
            placeholder="请选择团队成员"
            style="width: 100%"
            filterable
          >
            <el-option
              v-for="user in userList"
              :key="user.id"
              :label="user.username"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  Plus,
  Search,
  Refresh,
  MoreFilled,
  Edit,
  Delete,
} from "@element-plus/icons-vue";

export default {
  name: "TeamList",
  setup() {
    const loading = ref(false);
    const submitting = ref(false);
    const dialogVisible = ref(false);
    const isEdit = ref(false);
    const formRef = ref(null);

    const teamList = ref([]);
    const userList = ref([]);
    const currentPage = ref(1);
    const pageSize = ref(9);
    const total = ref(0);

    const searchForm = reactive({
      name: "",
    });

    const teamForm = reactive({
      id: null,
      name: "",
      description: "",
      members: [],
    });

    const formRules = {
      name: [
        { required: true, message: "请输入团队名称", trigger: "blur" },
        {
          min: 2,
          max: 20,
          message: "团队名称长度在 2 到 20 个字符",
          trigger: "blur",
        },
      ],
      members: [
        { required: true, message: "请选择团队成员", trigger: "change" },
      ],
    };

    const dialogTitle = computed(() => {
      return isEdit.value ? "编辑团队" : "创建团队";
    });

    // 模拟数据
    const mockTeams = [
      {
        id: 1,
        name: "前端开发团队",
        description: "负责前端界面开发和用户体验优化",
        memberCount: 5,
        projectCount: 3,
        members: [
          { id: 1, username: "张三", avatar: "" },
          { id: 2, username: "李四", avatar: "" },
          { id: 3, username: "王五", avatar: "" },
        ],
      },
      {
        id: 2,
        name: "后端开发团队",
        description: "负责后端服务开发和数据库设计",
        memberCount: 4,
        projectCount: 2,
        members: [
          { id: 4, username: "赵六", avatar: "" },
          { id: 5, username: "钱七", avatar: "" },
        ],
      },
    ];

    const mockUsers = [
      { id: 1, username: "张三" },
      { id: 2, username: "李四" },
      { id: 3, username: "王五" },
      { id: 4, username: "赵六" },
      { id: 5, username: "钱七" },
    ];

    // 获取团队列表
    const getTeamList = async () => {
      try {
        loading.value = true;
        // 模拟API调用
        setTimeout(() => {
          teamList.value = mockTeams;
          total.value = mockTeams.length;
          loading.value = false;
        }, 500);
      } catch (error) {
        ElMessage.error("获取团队列表失败");
        loading.value = false;
      }
    };

    // 获取用户列表
    const getUserList = async () => {
      try {
        userList.value = mockUsers;
      } catch (error) {
        console.error("获取用户列表失败");
      }
    };

    // 搜索
    const handleSearch = () => {
      currentPage.value = 1;
      getTeamList();
    };

    // 重置搜索
    const resetSearch = () => {
      searchForm.name = "";
      handleSearch();
    };

    // 分页
    const handleSizeChange = (size) => {
      pageSize.value = size;
      getTeamList();
    };

    const handleCurrentChange = (page) => {
      currentPage.value = page;
      getTeamList();
    };

    // 显示添加对话框
    const showAddDialog = () => {
      isEdit.value = false;
      dialogVisible.value = true;
    };

    // 编辑团队
    const handleEdit = (row) => {
      isEdit.value = true;
      Object.assign(teamForm, {
        id: row.id,
        name: row.name,
        description: row.description,
        members: row.members?.map((m) => m.id) || [],
      });
      dialogVisible.value = true;
    };

    // 删除团队
    const handleDelete = async (row) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除团队 "${row.name}" 吗？此操作不可恢复！`,
          "确认删除",
          { type: "warning" }
        );

        ElMessage.success("删除成功");
        getTeamList();
      } catch (error) {
        if (error !== "cancel") {
          ElMessage.error("删除失败");
        }
      }
    };

    // 查看成员
    const viewMembers = (team) => {
      ElMessage.info(`查看团队 "${team.name}" 的成员`);
    };

    // 提交表单
    const handleSubmit = async () => {
      try {
        const valid = await formRef.value.validate();
        if (!valid) return;

        submitting.value = true;

        if (isEdit.value) {
          ElMessage.success("团队更新成功");
        } else {
          ElMessage.success("团队创建成功");
        }

        dialogVisible.value = false;
        getTeamList();
      } catch (error) {
        ElMessage.error(error.message || "操作失败");
      } finally {
        submitting.value = false;
      }
    };

    // 重置表单
    const resetForm = () => {
      Object.assign(teamForm, {
        id: null,
        name: "",
        description: "",
        members: [],
      });
      formRef.value?.resetFields();
    };

    onMounted(() => {
      getTeamList();
      getUserList();
    });

    return {
      loading,
      submitting,
      dialogVisible,
      dialogTitle,
      isEdit,
      formRef,
      teamList,
      userList,
      currentPage,
      pageSize,
      total,
      searchForm,
      teamForm,
      formRules,
      getTeamList,
      handleSearch,
      resetSearch,
      handleSizeChange,
      handleCurrentChange,
      showAddDialog,
      handleEdit,
      handleDelete,
      viewMembers,
      handleSubmit,
      resetForm,
      Plus,
      Search,
      Refresh,
      MoreFilled,
      Edit,
      Delete,
    };
  },
};
</script>

<style lang="scss" scoped>
.team-list-container {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    margin: 0;
    color: #303133;
  }
}

.search-bar {
  margin-bottom: 20px;
  padding: 20px;
  background: #f5f7fa;
  border-radius: 4px;
}

.team-card {
  height: 200px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .team-name {
      font-weight: 600;
      color: #303133;
    }
  }

  .team-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    justify-content: space-between;

    .team-info {
      .description {
        margin: 0 0 10px 0;
        color: #606266;
        font-size: 14px;
        line-height: 1.4;
      }

      .stats {
        .el-tag + .el-tag {
          margin-left: 8px;
        }
      }
    }

    .team-members {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 15px;

      .member-avatars {
        display: flex;
        align-items: center;

        .more-members {
          margin-left: 8px;
          color: #909399;
          font-size: 12px;
        }
      }
    }
  }
}

.pagination {
  margin-top: 20px;
  text-align: center;
}
</style>
