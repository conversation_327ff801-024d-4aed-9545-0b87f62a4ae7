import authAPI from '@/api/auth'
import { setToken, getToken, removeToken } from '@/utils/auth'

const state = {
  token: getToken(),
  user: null,
  roles: []
}

const mutations = {
  SET_TOKEN(state, token) {
    state.token = token
  },
  SET_USER(state, user) {
    state.user = user
  },
  SET_ROLES(state, roles) {
    state.roles = roles
  },
  CLEAR_AUTH(state) {
    state.token = null
    state.user = null
    state.roles = []
  }
}

const actions = {
  async login({ commit }, credentials) {
    try {
      const response = await authAPI.login(credentials)
      const { data } = response.data
      
      commit('SET_TOKEN', data.accessToken)
      commit('SET_USER', {
        id: data.id,
        username: data.username,
        email: data.email
      })
      commit('SET_ROLES', data.roles)
      
      setToken(data.accessToken)
      return Promise.resolve(response)
    } catch (error) {
      return Promise.reject(error)
    }
  },

  async register({ commit }, userData) {
    try {
      const response = await authAPI.register(userData)
      return Promise.resolve(response)
    } catch (error) {
      return Promise.reject(error)
    }
  },

  logout({ commit }) {
    commit('CLEAR_AUTH')
    removeToken()
  },

  async checkAuth({ commit, state }) {
    console.log('Auth - checkAuth called, current token:', state.token)

    if (state.token) {
      try {
        // 验证token有效性
        // 这里可以调用后端API验证token
        // const response = await authAPI.verifyToken()

        // 为了测试，如果有token但没有用户信息，设置默认用户信息
        if (!state.user) {
          commit('SET_USER', {
            id: 1,
            username: 'admin',
            email: '<EMAIL>'
          })
          commit('SET_ROLES', ['ROLE_ADMIN', 'ROLE_MANAGER'])
          console.log('Auth - Set default user for existing token')
        }
      } catch (error) {
        console.error('Token verification failed:', error)
        commit('CLEAR_AUTH')
        removeToken()
      }
    }
    // 移除自动设置临时用户的逻辑，避免路由循环
    console.log('Auth - checkAuth completed, isAuthenticated:', !!state.token)
  }
}

const getters = {
  isAuthenticated: state => !!state.token,
  currentUser: state => state.user,
  userRoles: state => state.roles,
  hasRole: state => role => state.roles.includes(role),
  isAdmin: state => state.roles.includes('ROLE_ADMIN'),
  isManager: state => state.roles.includes('ROLE_MANAGER') || state.roles.includes('ROLE_ADMIN')
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}